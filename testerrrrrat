from playwright.sync_api import sync_playwright, expect
import json
import time
import logging
from dataclasses import dataclass, field
from typing import List, Dict, Optional
from datetime import datetime
import ollama
import hashlib
import os

@dataclass
class TestResult:
    test_name: str
    status: str  # PASSED, FAILED, WARNING, ERROR
    details: str
    screenshot_path: str
    execution_time: float = 0.0
    severity: str = "LOW"  # LOW, MEDIUM, HIGH, CRITICAL
    recommendations: List[str] = field(default_factory=list)

@dataclass
class TestConfig:
    headless: bool = True
    viewport_width: int = 1920
    viewport_height: int = 1080
    timeout: int = 30000
    performance_thresholds: Dict = field(default_factory=lambda: {
        'load_time': 3000,
        'first_contentful_paint': 2000,
        'largest_contentful_paint': 4000
    })

class AIWebTester:
    def __init__(self, config: TestConfig = None):
        self.config = config or TestConfig()
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(
            headless=self.config.headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = self.browser.new_context(
            viewport={'width': self.config.viewport_width, 'height': self.config.viewport_height}
        )
        self.results: List[TestResult] = []
        self.setup_logging()
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('web_testing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def run_comprehensive_test(self, url: str, custom_tests: List = None):
        """Run comprehensive web testing suite"""
        start_time = time.time()
        self.logger.info(f"Starting comprehensive test for: {url}")
        
        page = self.context.new_page()
        
        # Enable detailed metrics
        page.route('**/*', self.track_network_requests)
        
        try:
            page.goto(url, wait_until='networkidle', timeout=self.config.timeout)
        except Exception as e:
            self.results.append(TestResult(
                "page_load", "ERROR", f"Failed to load page: {str(e)}", 
                "", 0, "CRITICAL", ["Check URL accessibility and network connectivity"]
            ))
            return
        
        # Standard test suite
        tests = custom_tests or [
            self.test_page_structure,
            self.test_accessibility_comprehensive,
            self.test_forms_advanced,
            self.test_navigation_comprehensive,
            self.test_responsive_design,
            self.test_performance_comprehensive,
            self.test_security_comprehensive,
            self.test_seo_basics,
            self.test_browser_compatibility,
            self.test_user_experience
        ]
        
        for test in tests:
            test_start = time.time()
            try:
                self.logger.info(f"Running test: {test.__name__}")
                result = test(page)
                result.execution_time = time.time() - test_start
                self.results.append(result)
            except Exception as e:
                self.results.append(TestResult(
                    test_name=test.__name__,
                    status="ERROR",
                    details=str(e),
                    screenshot_path=self.take_screenshot(page, test.__name__),
                    execution_time=time.time() - test_start,
                    severity="HIGH",
                    recommendations=[f"Fix error in {test.__name__}: {str(e)}"]
                ))
        
        page.close()
        total_time = time.time() - start_time
        self.logger.info(f"Testing completed in {total_time:.2f} seconds")
    
    def test_page_structure(self, page):
        """Test HTML structure and semantic elements"""
        issues = []
        recommendations = []
        
        # Check for semantic HTML
        has_header = page.query_selector('header') is not None
        has_nav = page.query_selector('nav') is not None
        has_main = page.query_selector('main') is not None
        has_footer = page.query_selector('footer') is not None
        
        if not has_header:
            issues.append("Missing <header> element")
            recommendations.append("Add semantic <header> element")
        if not has_main:
            issues.append("Missing <main> element")
            recommendations.append("Add semantic <main> element for primary content")
        
        # Check title and meta description
        title = page.title()
        if not title or len(title) < 10:
            issues.append("Page title missing or too short")
            recommendations.append("Add descriptive page title (50-60 characters)")
        
        meta_description = page.query_selector('meta[name="description"]')
        if not meta_description:
            issues.append("Missing meta description")
            recommendations.append("Add meta description for SEO")
        
        # Check heading hierarchy
        headings = page.query_selector_all('h1, h2, h3, h4, h5, h6')
        h1_count = len(page.query_selector_all('h1'))
        
        if h1_count == 0:
            issues.append("No H1 heading found")
            recommendations.append("Add exactly one H1 heading per page")
        elif h1_count > 1:
            issues.append(f"Multiple H1 headings found ({h1_count})")
            recommendations.append("Use only one H1 heading per page")
        
        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "page_structure", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_accessibility_comprehensive(self, page):
        """Comprehensive accessibility testing"""
        issues = []
        recommendations = []
        
        # Images without alt text
        images_without_alt = page.query_selector_all('img:not([alt])')
        if images_without_alt:
            issues.append(f"{len(images_without_alt)} images missing alt text")
            recommendations.append("Add descriptive alt attributes to all images")
        
        # Form accessibility
        inputs_without_labels = page.query_selector_all('input:not([aria-label]):not([aria-labelledby])')
        unlabeled = []
        for inp in inputs_without_labels:
            input_id = inp.get_attribute('id')
            if not input_id or not page.query_selector(f'label[for="{input_id}"]'):
                unlabeled.append(inp)
        
        if unlabeled:
            issues.append(f"{len(unlabeled)} form inputs without proper labels")
            recommendations.append("Associate labels with form inputs using 'for' attribute or aria-label")
        
        # Color contrast (basic check)
        low_contrast_elements = page.evaluate("""
            () => {
                const elements = document.querySelectorAll('*');
                let lowContrastCount = 0;
                // Basic contrast check would go here
                return lowContrastCount;
            }
        """)
        
        # Keyboard navigation
        focusable_elements = page.query_selector_all('a, button, input, select, textarea, [tabindex]')
        if len(focusable_elements) == 0:
            issues.append("No focusable elements found")
            recommendations.append("Ensure interactive elements are keyboard accessible")
        
        # ARIA landmarks
        landmarks = page.query_selector_all('[role="banner"], [role="navigation"], [role="main"], [role="contentinfo"]')
        if len(landmarks) < 2:
            issues.append("Insufficient ARIA landmarks")
            recommendations.append("Add ARIA landmarks for better screen reader navigation")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if len(issues) > 3 else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "accessibility_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_forms_advanced(self, page):
        """Advanced form testing with AI-generated test cases"""
        forms = page.query_selector_all('form')
        if not forms:
            return TestResult("forms_advanced", "PASSED", "No forms found", "", 0, "LOW", [])
        
        issues = []
        recommendations = []
        
        for i, form in enumerate(forms):
            form_issues = []
            
            # Check form structure
            inputs = form.query_selector_all('input, textarea, select')
            submit_buttons = form.query_selector_all('button[type="submit"], input[type="submit"]')
            
            if not submit_buttons:
                form_issues.append("Form missing submit button")
                recommendations.append("Add submit button to form")
            
            # Test form validation
            for input_elem in inputs:
                input_type = input_elem.get_attribute('type') or 'text'
                required = input_elem.get_attribute('required') is not None
                
                # Test with invalid data
                if input_type == 'email':
                    try:
                        input_elem.fill('invalid-email')
                        if submit_buttons:
                            submit_buttons[0].click()
                            page.wait_for_timeout(1000)
                            
                        # Check for validation message
                        validation_msg = page.query_selector('.error, .invalid, [aria-invalid="true"]')
                        if not validation_msg and required:
                            form_issues.append(f"Email input lacks proper validation")
                    except:
                        pass
            
            if form_issues:
                issues.extend([f"Form {i+1}: {issue}" for issue in form_issues])
        
        # AI-powered test case generation
        if forms:
            ai_test_results = self.generate_and_execute_ai_form_tests(page, forms[0])
            if ai_test_results.get('issues'):
                issues.extend(ai_test_results['issues'])
                recommendations.extend(ai_test_results.get('recommendations', []))
        
        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "forms_advanced", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_performance_comprehensive(self, page):
        """Comprehensive performance testing"""
        issues = []
        recommendations = []
        
        # Navigation timing
        timing = page.evaluate("""
            () => {
                const perf = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: perf.domContentLoadedEventEnd - perf.navigationStart,
                    loadComplete: perf.loadEventEnd - perf.navigationStart,
                    firstContentfulPaint: performance.getEntriesByType('paint')
                        .find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
                };
            }
        """)
        
        # Check load times
        if timing['loadComplete'] > self.config.performance_thresholds['load_time']:
            issues.append(f"Slow page load: {timing['loadComplete']:.0f}ms")
            recommendations.append("Optimize page load time (target: <3s)")
        
        # Resource analysis
        resources = page.evaluate("""
            () => {
                const entries = performance.getEntriesByType('resource');
                return {
                    total: entries.length,
                    large_resources: entries.filter(e => e.transferSize > 100000).length,
                    slow_resources: entries.filter(e => e.duration > 1000).length
                };
            }
        """)
        
        if resources['large_resources'] > 5:
            issues.append(f"{resources['large_resources']} large resources (>100KB)")
            recommendations.append("Compress large resources and optimize images")
        
        # Image optimization
        image_analysis = page.evaluate("""
            () => {
                const images = Array.from(document.images);
                return {
                    total: images.length,
                    oversized: images.filter(img => img.naturalWidth > 2000 || img.naturalHeight > 2000).length,
                    missing_lazy: images.filter(img => !img.loading || img.loading !== 'lazy').length
                };
            }
        """)
        
        if image_analysis['oversized'] > 0:
            issues.append(f"{image_analysis['oversized']} oversized images")
            recommendations.append("Resize images appropriately for web use")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if timing['loadComplete'] > 5000 else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "performance_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_security_comprehensive(self, page):
        """Comprehensive security testing"""
        issues = []
        recommendations = []
        
        # HTTPS check
        url = page.url
        if not url.startswith('https://'):
            issues.append("Page not served over HTTPS")
            recommendations.append("Implement SSL/TLS encryption")
        
        # Security headers check
        response = page.evaluate("""
            async () => {
                try {
                    const response = await fetch(window.location.href);
                    const headers = {};
                    response.headers.forEach((value, key) => {
                        headers[key] = value;
                    });
                    return headers;
                } catch (e) {
                    return {};
                }
            }
        """)
        
        security_headers = [
            'x-frame-options',
            'x-content-type-options',
            'strict-transport-security',
            'content-security-policy'
        ]
        
        missing_headers = [header for header in security_headers if header not in response]
        if missing_headers:
            issues.append(f"Missing security headers: {', '.join(missing_headers)}")
            recommendations.append("Implement security headers for better protection")
        
        # Form security
        forms = page.query_selector_all('form')
        for form in forms:
            action = form.get_attribute('action')
            method = form.get_attribute('method')
            
            if method and method.upper() == 'GET' and 'password' in form.inner_html().lower():
                issues.append("Password form using GET method")
                recommendations.append("Use POST method for sensitive form data")
        
        # External links
        external_links = page.query_selector_all('a[href^="http"]:not([rel*="noopener"])')
        if external_links:
            issues.append(f"{len(external_links)} external links without rel='noopener'")
            recommendations.append("Add rel='noopener' to external links")
        
        status = "FAILED" if issues else "PASSED"
        severity = "HIGH" if not url.startswith('https://') else "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "security_comprehensive", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_responsive_design(self, page):
        """Test responsive design across different viewports"""
        issues = []
        recommendations = []
        
        viewports = [
            {'width': 320, 'height': 568, 'name': 'Mobile'},
            {'width': 768, 'height': 1024, 'name': 'Tablet'},
            {'width': 1920, 'height': 1080, 'name': 'Desktop'}
        ]
        
        original_viewport = page.viewport_size
        
        for viewport in viewports:
            page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
            page.wait_for_timeout(500)  # Allow layout to settle
            
            # Check for horizontal scrollbar
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                issues.append(f"Horizontal scroll on {viewport['name']}")
                recommendations.append(f"Fix horizontal overflow on {viewport['name']} devices")
            
            # Check for mobile-specific issues
            if viewport['name'] == 'Mobile':
                # Check viewport meta tag
                viewport_meta = page.query_selector('meta[name="viewport"]')
                if not viewport_meta:
                    issues.append("Missing viewport meta tag")
                    recommendations.append("Add viewport meta tag for mobile optimization")
                
                # Check touch targets
                buttons = page.query_selector_all('button, a, input[type="button"], input[type="submit"]')
                for button in buttons[:5]:  # Check first 5 buttons
                    box = button.bounding_box()
                    if box and (box['width'] < 44 or box['height'] < 44):
                        issues.append("Touch targets too small (< 44px)")
                        recommendations.append("Increase touch target size to at least 44x44px")
                        break
        
        # Reset viewport
        page.set_viewport_size(original_viewport)
        
        status = "FAILED" if issues else "PASSED"
        severity = "MEDIUM" if issues else "LOW"
        
        return TestResult(
            "responsive_design", status, "; ".join(issues),
            "", 0, severity, recommendations
        )
    
    def test_seo_basics(self, page):
        """Basic SEO testing"""
        issues = []
        recommendations = []
        
        # Title optimization
        title = page.title()
        if not title:
            issues.append("Missing page title")
            recommendations.append("Add descriptive page title")
        elif len(title) > 60:
            issues.append("Title too long")
            recommendations.append("Keep title under 60 characters")
        
        # Meta description
        meta_desc = page.query_selector('meta[name="description"]')
        if meta_desc:
            content = meta_desc.get_attribute('content')
            if len(content) > 160:
                issues.append("Meta description too long")
                recommendations.append("Keep meta description under 160 characters")
        
        # Structured data
        structured_data = page.query_selector_all('script[type="application/ld+json"]')
        if not structured_data:
            recommendations.append("Consider adding structured data for better SEO")
        
        # Internal linking
        internal_links = page.query_selector_all('a[href^="/"], a[href^="./"]')
        if len(internal_links) < 3:
            issues.append("Limited internal linking")
            recommendations.append("Add more internal links for better SEO")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "seo_basics", status, "; ".join(issues),
            "", 0, "LOW", recommendations
        )
    
    def test_user_experience(self, page):
        """User experience testing"""
        issues = []
        recommendations = []
        
        # Loading indicators
        page.reload(wait_until='domcontentloaded')
        has_loading_indicator = page.query_selector('.loading, .spinner, [aria-label*="loading"]')
        
        # 404 error handling
        broken_links = []
        links = page.query_selector_all('a[href]')[:10]  # Test first 10 links
        for link in links:
            href = link.get_attribute('href')
            if href and href.startswith('http'):
                try:
                    response = page.goto(href, wait_until='domcontentloaded', timeout=5000)
                    if response and response.status >= 400:
                        broken_links.append(href)
                except:
                    broken_links.append(href)
        
        if broken_links:
            issues.append(f"{len(broken_links)} broken links found")
            recommendations.append("Fix or remove broken links")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "user_experience", status, "; ".join(issues),
            "", 0, "MEDIUM" if issues else "LOW", recommendations
        )
    
    def test_navigation_comprehensive(self, page):
        """Comprehensive navigation testing"""
        issues = []
        recommendations = []
        
        # Check for navigation element
        nav_elements = page.query_selector_all('nav, [role="navigation"]')
        if not nav_elements:
            issues.append("No navigation element found")
            recommendations.append("Add semantic navigation element")
        
        # Check navigation links
        nav_links = page.query_selector_all('nav a, [role="navigation"] a')
        if len(nav_links) < 3:
            issues.append("Limited navigation options")
            recommendations.append("Provide adequate navigation options")
        
        # Breadcrumb check
        breadcrumbs = page.query_selector('[aria-label*="breadcrumb"], .breadcrumb, nav[aria-label*="breadcrumb"]')
        if not breadcrumbs and len(nav_links) > 5:
            recommendations.append("Consider adding breadcrumb navigation for deep
        if not breadcrumbs and len(nav_links) > 5:
            recommendations.append("Consider adding breadcrumb navigation for deep site structures")
        
        # Test keyboard navigation
        try:
            first_link = nav_links[0] if nav_links else page.query_selector('a')
            if first_link:
                first_link.focus()
                page.keyboard.press('Tab')
                focused_element = page.evaluate('document.activeElement.tagName')
                if not focused_element:
                    issues.append("Keyboard navigation not working properly")
                    recommendations.append("Ensure all interactive elements are keyboard accessible")
        except:
            pass
        
        # Check for skip links
        skip_link = page.query_selector('a[href="#main"], a[href="#content"], .skip-link')
        if not skip_link:
            recommendations.append("Add skip navigation link for accessibility")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "navigation_comprehensive", status, "; ".join(issues),
            "", 0, "MEDIUM" if issues else "LOW", recommendations
        )
    
    def test_browser_compatibility(self, page):
        """Test browser-specific features and compatibility"""
        issues = []
        recommendations = []
        
        # Check for modern JS features without fallbacks
        js_errors = page.evaluate("""
            () => {
                const errors = [];
                
                // Check for console errors
                window.addEventListener('error', (e) => {
                    errors.push(e.message);
                });
                
                // Test common compatibility issues
                try {
                    // ES6 features
                    eval('const test = () => {};');
                    eval('const {x} = {x: 1};');
                } catch (e) {
                    errors.push('ES6 syntax not supported');
                }
                
                return errors;
            }
        """)
        
        if js_errors:
            issues.append(f"JavaScript compatibility issues: {'; '.join(js_errors[:3])}")
            recommendations.append("Add polyfills or transpile modern JavaScript")
        
        # Check CSS compatibility
        css_issues = page.evaluate("""
            () => {
                const issues = [];
                const styles = getComput
        # Check CSS compatibility
        css_issues = page.evaluate("""
            () => {
                const issues = [];
                const testElement = document.createElement('div');
                document.body.appendChild(testElement);
                
                // Test modern CSS features
                const cssTests = [
                    ['grid', 'display: grid'],
                    ['flexbox', 'display: flex'],
                    ['css-variables', '--test: red'],
                    ['transforms', 'transform: rotate(45deg)']
                ];
                
                cssTests.forEach(([feature, css]) => {
                    testElement.style.cssText = css;
                    const computed = getComputedStyle(testElement);
                    if (!computed.getPropertyValue(css.split(':')[0].trim())) {
                        issues.push(`${feature} not supported`);
                    }
                });
                
                document.body.removeChild(testElement);
                return issues;
            }
        """)
        
        if css_issues:
            issues.extend(css_issues[:3])  # Limit to first 3 issues
            recommendations.append("Add CSS fallbacks for better browser support")
        
        status = "FAILED" if issues else "PASSED"
        
        return TestResult(
            "browser_compatibility", status, "; ".join(issues),
            "", 0, "LOW", recommendations
        )
    
    def generate_and_execute_ai_form_tests(self, page, form):
        """Generate and execute AI-powered form tests"""
        try:
            form_html = form.inner_html()
            form_structure = self.analyze_form_structure(form)
            
            prompt = f"""
            Analyze this form and generate comprehensive test cases:
            
            Form HTML: {form_html[:1000]}...
            
            Form Structure: {json.dumps(form_structure, indent=2)}
            
            Generate test cases in JSON format for:
            1. Valid input scenarios
            2. Invalid input scenarios (boundary testing)
            3. Security testing (XSS, SQL injection attempts)
            4. Accessibility testing
            5. Usability testing
            
            Response format:
            {{
                "test_cases": [
                    {{
                        "name": "test_name",
                        "selector": "css_selector",
                        "input_value": "test_value",
                        "expected_result": "expected_outcome",
                        "test_type": "validation|security|accessibility"
                    }}
                ]
            }}
            """
            
            # Use Ollama for AI analysis
            response = ollama.chat(model='llama2', messages=[
                {'role': 'user', 'content': prompt}
            ])
            
            ai_tests = json.loads(response['message']['content'])
            return self.execute_ai_test_cases(page, form, ai_tests['test_cases'])
            
        except Exception as e:
            self.logger.warning(f"AI form test generation failed: {e}")
            return self.execute_default_form_tests(page, form)
    
    def analyze_form_structure(self, form):
        """Analyze form structure for AI context"""
        inputs = form.query_selector_all('input, textarea, select')
        structure = []
        
        for inp in inputs:
            structure.append({
                'tag': inp.tag_name,
                'type': inp.get_attribute('type'),
                'name': inp.get_attribute('name'),
                'id': inp.get_attribute('id'),
                'required': inp.get_attribute('required') is not None,
                'placeholder': inp.get_attribute('placeholder')
            })
        
        return structure
    
    def execute_ai_test_cases(self, page, form, test_cases):
        """Execute AI-generated test cases"""
        issues = []
        recommendations = []
        
        for test_case in test_cases[:10]:  # Limit to 10 test cases
            try:
                selector = test_case.get('selector')
                input_value = test_case.get('input_value')
                test_type = test_case.get('test_type', 'validation')
                
                element = form.query_selector(selector)
                if not element:
                    continue
                
                # Execute test
                element.clear()
                element.fill(input_value)
                
                # Check for immediate validation feedback
                page.wait_for_timeout(500)
                validation_msg = page.query_selector('.error, .invalid, [aria-invalid="true"]')
                
                if test_type == 'security' and not validation_msg:
                    issues.append(f"Security test failed: {test_case.get('name')}")
                    recommendations.append("Implement proper input sanitization")
                
            except Exception as e:
                self.logger.debug(f"Test case execution failed: {e}")
        
        return {'issues': issues, 'recommendations': recommendations}
    
    def execute_default_form_tests(self, page, form):
        """Fallback form testing when AI is unavailable"""
        issues = []
        recommendations = []
        
        # Test common input types
        email_inputs = form.query_selector_all('input[type="email"]')
        for email_input in email_inputs:
            try:
                email_input.fill('invalid-email')
                page.wait_for_timeout(500)
                if not page.query_selector('.error, [aria-invalid="true"]'):
                    issues.append("Email validation missing")
                    recommendations.append("Add email format validation")
            except:
                pass
        
        # Test required fields
        required_inputs = form.query_selector_all('input[required], textarea[required]')
        required_inputs = form.query_selector_all('input[required], textarea[required]')
        for req_input in required_inputs:
            try:
                req_input.clear()
                # Try to submit form with empty required field
                submit_btn = form.query_selector('button[type="submit"], input[type="submit"]')
                if submit_btn:
                    submit_btn.click()
                    page.wait_for_timeout(500)
                    if not page.query_selector('.error, [aria-invalid="true"]'):
                        issues.append("Required field validation missing")
                        recommendations.append("Add validation for required fields")
            except:
                pass
        
        return {'issues': issues, 'recommendations': recommendations}
    
    def track_network_requests(self, route):
        """Track network requests for performance analysis"""
        response = route.continue_()
        # Log slow requests, failed requests, etc.
        return response
    
    def take_screenshot(self, page, test_name=""):
        """Take screenshot with timestamp and test name"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{test_name}_{timestamp}.png"
        path = os.path.join("screenshots", filename)
        
        # Create screenshots directory if it doesn't exist
        os.makedirs("screenshots", exist_ok=True)
        
        try:
            page.screenshot(path=path, full_page=True)
            return path
        except:
            return ""
    
    def generate_comprehensive_report(self):
        """Generate detailed HTML report with metrics and recommendations"""
        passed = len([r for r in self.results if r.status == "PASSED"])
        failed = len([r for r in self.results if r.status == "FAILED"])
        warnings = len([r for r in self.results if r.status == "WARNING"])
        errors = len([r for r in self.results if r.status == "ERROR"])
        
        total_execution_time = sum(r.execution_time for r in self.results)
        
        # Calculate severity distribution
        severity_counts = {}
        for result in self.results:
            severity_counts[result.severity] = severity_counts.get(result.severity, 0) + 1
        
        # Generate HTML report
        html_report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Web Testing Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; }}
                .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
                .metric {{ background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }}
                .passed {{ border-left: 4px solid #28a745; }}
                .failed {{ border-left: 4px solid #dc3545; }}
                .warning {{ border-left: 4px solid #ffc107; }}
                .error {{ border-left: 4px solid #6f42c1; }}
                .test-result {{ margin: 10px 0; padding: 15px; border-radius: 5px; }}
                .recommendations {{ background: #e3f2fd; padding: 10px; border-radius: 3px; margin-top: 10px; }}
                .critical {{ background: #ffebee; }}
                .high {{ background: #fff3e0; }}
                .medium {{ background: #f3e5f5; }}
                .low {{ background: #e8f5e8; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Web Testing Report</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Total Execution Time: {total_execution_time:.2f} seconds</p>
            </div>
            
            <div class="summary">
                <div class="metric passed">
                    <h3>{passed}</h3>
                    <p>Passed</p>
                </div>
                <div class="metric failed">
                    <h3>{failed}</h3>
                    <p>Failed</p>
                </div>
                <div class="metric warning">
                    <h3>{warnings}</h3>
                    <p>Warnings</p>
                </div>
                <div class="metric error">
                    <h3>{errors}</h3>
                    <p>Errors</p>
                </div>
            </div>
            
            <h2>Severity Distribution</h2>
            <ul>
        """
        
        for severity, count in severity_counts.items():
            html_report += f"<li>{severity}: {count} issues</li>"
        
        html_report += """
            </ul>
            
            <h2>Detailed Results</h2>
        """
        
        for result in self.results:
            status_class = result.status.lower()
            severity_class = result.severity.lower()
            
            html_report += f"""
            <div class="test-result {status_class} {severity_class}">
                <h3>{result.test_name.replace('_', ' ').title()}</h3>
                <p><strong>Status:</strong> {result.status}</p>
                <p><strong>Severity:</strong> {result.severity}</p>
                <p><strong>Execution Time:</strong> {result.execution_time:.2f}s</p>
                {f'<p><strong>Details:</strong> {result.details}</p>' if result.details else ''}
                
                {f'<div class="recommendations"><h4>Recommendations:</h4><ul>' + ''.join([f'<li>{rec}</li>' for rec in result.recommendations]) + '</ul></div>' if result.recommendations else ''}
                
                {f'<p><strong>Screenshot:</strong> <a href="{result.screenshot_path}" target="_blank">View Screenshot</a></p>' if result.screenshot_path else ''}
            </div>
            """
        
        html_report += """
        </body>
        </html>
        """
        
        # Save HTML report
        with open(f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html", "w") as f:
            f.write(html_report)
        
        return html_report
    
    def generate_json_report(self):
        """Generate machine-readable JSON report"""
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'passed': len([r for r in self.results if r.status == "PASSED"]),
                'failed': len([r for r in self.results if r.status == "FAILED"]),
                'warnings': len([r for r in self.results if r.status == "WARNING"]),
                'errors': len([r for r in self.results if r.status == "ERROR"]),
                'total_execution_time': sum(r.execution_time for r in self.results)
            },
            'results': [
                {
                    'test_name': r.test_name,
                    'status': r.status,
                    'severity': r.severity,
                    'details': r.details,
                    'execution_time': r.execution_time,
                    'recommendations': r.recommendations,
                    'screenshot_path': r.screenshot_path
                }
                for r in self.results
            ]
        }
        
        filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        return report_data
    
    def cleanup(self):
        """Clean up browser resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

# Enhanced usage example with configuration
if __name__ == "__main__":
    # Custom configuration
    config = TestConfig(
        headless=True,
        viewport_width=1920,
        viewport_height=1080,
        performance_thresholds={
            'load_time': 2500,     # More strict
            'first_contentful_paint': 1500,
            'largest_contentful_paint': 3000
        }
    )
    
    tester = AIWebTester(config)
    
    try:
        # Test multiple URLs
        urls_to_test = [
            "https://example.com",
            "https://github.com",
            "https://stackoverflow.com"
        ]
        
        for url in urls_to_test:
            print(f"\n=== Testing {url} ===")
            tester.run_comprehensive_test(url)
        
        # Generate reports
        html_report = tester.generate_comprehensive_report()
        json_report = tester.generate_json_report()
        
        print(f"\nTesting completed! Generated reports:")
        print(f"- HTML report created")
        print(f"- JSON report created")
        print(f"- Total tests run: {len(tester.results)}")
        
        # Print summary
        summary = json_report['summary']
        print(f"\nSummary:")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"⚠️  Warnings: {summary['warnings']}")
        print(f"🚫 Errors: {summary['errors']}")
        
    finally:
        tester.cleanup()
