import { NextRequest, NextResponse } from 'next/server';
import { getCSRFToken } from '@/lib/csrf';
import { SecurityMiddleware } from '@/lib/security-middleware';

export async function GET(request: NextRequest) {
  return SecurityMiddleware.secureRead(
    request,
    async () => {
      try {
        const csrfToken = await getCSRFToken(request);

        return NextResponse.json(
          {
            csrfToken,
            timestamp: Date.now()
          },
          {
            status: 200,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
              'Pragma': 'no-cache'
            }
          }
        );
      } catch (error) {
        console.error('CSRF token generation error:', error);
        return NextResponse.json(
          { error: 'Failed to generate CSRF token' },
          { status: 500 }
        );
      }
    },
    {
      rateLimitType: 'api',
      requireCSRF: false
    }
  );
}
