import { NextRequest, NextResponse } from 'next/server';
import { SecurityValidator } from './validation';

/**
 * Simple security utilities for immediate implementation
 */
export class SimpleSecurity {
  /**
   * Basic input sanitization
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') return '';
    
    // Remove dangerous patterns
    let sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/['";]/g, '') // Remove quotes and semicolons
      .trim();
    
    // Limit length
    if (sanitized.length > 1000) {
      sanitized = sanitized.slice(0, 1000);
    }
    
    return sanitized;
  }

  /**
   * Validate email with length limits
   */
  static validateEmail(email: string): { isValid: boolean; message?: string } {
    if (!email || typeof email !== 'string') {
      return { isValid: false, message: 'Email is required' };
    }

    const sanitized = this.sanitizeInput(email);
    
    if (sanitized.length > 254) {
      return { isValid: false, message: 'Email too long' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitized)) {
      return { isValid: false, message: 'Invalid email format' };
    }

    return { isValid: true };
  }

  /**
   * Basic CSRF token generation
   */
  static generateCSRFToken(): string {
    return crypto.randomUUID() + '-' + Date.now().toString(36);
  }

  /**
   * Simple rate limiting check
   */
  static checkRateLimit(identifier: string, maxRequests: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    // This is a simple in-memory rate limiter
    // In production, you'd want to use Redis or a database
    const now = Date.now();
    const key = `rate_limit_${identifier}`;
    
    // Get existing data from memory (simplified)
    const stored = global[key as keyof typeof global] as { count: number; resetTime: number } | undefined;
    
    if (!stored || now > stored.resetTime) {
      // Reset window
      global[key as keyof typeof global] = { count: 1, resetTime: now + windowMs };
      return true;
    }

    if (stored.count >= maxRequests) {
      return false; // Rate limit exceeded
    }

    stored.count++;
    return true;
  }

  /**
   * Add security headers to response
   */
  static addSecurityHeaders(response: NextResponse): NextResponse {
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Basic CSP
    response.headers.set('Content-Security-Policy', 
      "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    
    return response;
  }

  /**
   * Validate request body for security threats
   */
  static validateRequestBody(body: any): { isValid: boolean; threats: string[] } {
    if (!body) return { isValid: true, threats: [] };

    const bodyString = JSON.stringify(body);
    return SecurityValidator.validateSecurity(bodyString);
  }
}

/**
 * Simple middleware wrapper for API routes
 */
export function withSimpleSecurity(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    requireCSRF?: boolean;
    rateLimit?: { max: number; windowMs: number };
  } = {}
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Rate limiting
      if (options.rateLimit) {
        const clientIP = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
        const allowed = SimpleSecurity.checkRateLimit(
          clientIP, 
          options.rateLimit.max, 
          options.rateLimit.windowMs
        );
        
        if (!allowed) {
          return NextResponse.json(
            { error: 'Too many requests' },
            { status: 429 }
          );
        }
      }

      // CSRF check for state-changing operations
      if (options.requireCSRF && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
        const csrfToken = req.headers.get('x-csrf-token');
        if (!csrfToken) {
          return NextResponse.json(
            { error: 'CSRF token required' },
            { status: 403 }
          );
        }
      }

      // Validate request body
      if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
        try {
          const body = await req.json();
          const validation = SimpleSecurity.validateRequestBody(body);
          
          if (!validation.isValid) {
            console.warn('Security threats detected:', validation.threats);
            return NextResponse.json(
              { error: 'Invalid input detected' },
              { status: 400 }
            );
          }
        } catch (error) {
          // If JSON parsing fails, continue (might be form data)
        }
      }

      // Call the original handler
      const response = await handler(req);
      
      // Add security headers
      return SimpleSecurity.addSecurityHeaders(response);

    } catch (error) {
      console.error('Security middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Enhanced form validation with security
 */
export function validateFormData(data: Record<string, any>): {
  isValid: boolean;
  sanitizedData: Record<string, any>;
  errors: string[];
} {
  const errors: string[] = [];
  const sanitizedData: Record<string, any> = {};

  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      const sanitized = SimpleSecurity.sanitizeInput(value);
      sanitizedData[key] = sanitized;
      
      // Check for potential threats
      const validation = SecurityValidator.validateSecurity(value);
      if (!validation.isValid) {
        errors.push(`${key}: ${validation.threats.join(', ')}`);
      }
    } else {
      sanitizedData[key] = value;
    }
  }

  return {
    isValid: errors.length === 0,
    sanitizedData,
    errors
  };
}

export default SimpleSecurity;
