#!/usr/bin/env python3
"""
Security Fixes Validation Script
Tests the implemented security enhancements to ensure vulnerabilities are fixed
"""

import requests
import json
import time
import sys
from datetime import datetime

class SecurityTester:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self):
        """Get CSRF token from the server"""
        try:
            response = self.session.get(f"{self.base_url}/api/csrf-token")
            if response.status_code == 200:
                data = response.json()
                self.csrf_token = data.get('csrfToken')
                return True
            return False
        except Exception as e:
            print(f"❌ Failed to get CSRF token: {e}")
            return False
    
    def test_csrf_protection(self):
        """Test CSRF protection implementation"""
        print("\n🔒 Testing CSRF Protection...")
        
        # Test 1: Request without CSRF token should fail
        response = self.session.post(
            f"{self.base_url}/api/signup",
            json={"email": "<EMAIL>", "password": "TestPass123!"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 403:
            print("✅ CSRF protection active - requests without token rejected")
        else:
            print(f"❌ CSRF protection failed - status: {response.status_code}")
            return False
        
        # Test 2: Request with valid CSRF token should work
        if self.get_csrf_token():
            response = self.session.post(
                f"{self.base_url}/api/signup",
                json={"email": "<EMAIL>", "password": "TestPass123!"},
                headers={
                    "Content-Type": "application/json",
                    "X-CSRF-Token": self.csrf_token
                }
            )
            
            if response.status_code in [201, 409]:  # 201 created or 409 user exists
                print("✅ CSRF token validation working")
            else:
                print(f"❌ CSRF token validation failed - status: {response.status_code}")
                return False
        
        return True
    
    def test_input_sanitization(self):
        """Test input sanitization against XSS and injection attacks"""
        print("\n🧼 Testing Input Sanitization...")
        
        malicious_payloads = [
            "<script>alert('XSS')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "%3Cscript%3Ealert('XSS')%3C/script%3E"
        ]
        
        if not self.get_csrf_token():
            print("❌ Cannot test without CSRF token")
            return False
        
        for payload in malicious_payloads:
            response = self.session.post(
                f"{self.base_url}/api/signup",
                json={"email": payload, "password": "TestPass123!"},
                headers={
                    "Content-Type": "application/json",
                    "X-CSRF-Token": self.csrf_token
                }
            )
            
            # Check if malicious payload is rejected or sanitized
            if response.status_code == 400:
                print(f"✅ Malicious payload rejected: {payload[:30]}...")
            elif response.status_code == 403:
                print(f"✅ Security validation blocked: {payload[:30]}...")
            else:
                print(f"⚠️ Payload processed (may be sanitized): {payload[:30]}...")
        
        return True
    
    def test_rate_limiting(self):
        """Test rate limiting implementation"""
        print("\n⏱️ Testing Rate Limiting...")
        
        if not self.get_csrf_token():
            print("❌ Cannot test without CSRF token")
            return False
        
        # Make multiple rapid requests to trigger rate limiting
        for i in range(12):  # Auth limit is 10 per 15 minutes
            response = self.session.post(
                f"{self.base_url}/api/signup",
                json={"email": f"test{i}@example.com", "password": "TestPass123!"},
                headers={
                    "Content-Type": "application/json",
                    "X-CSRF-Token": self.csrf_token
                }
            )
            
            if response.status_code == 429:
                print(f"✅ Rate limiting active - blocked after {i+1} requests")
                return True
            elif i == 11:
                print("⚠️ Rate limiting may not be working - all requests processed")
                return False
        
        return True
    
    def test_session_security(self):
        """Test session management security"""
        print("\n🔑 Testing Session Security...")
        
        # Test session regeneration on login
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPass123!"
        }
        
        if not self.get_csrf_token():
            print("❌ Cannot test without CSRF token")
            return False
        
        # Attempt login (may fail if user doesn't exist, but we can check headers)
        response = self.session.post(
            f"{self.base_url}/api/auth/signin",
            json=login_data,
            headers={
                "Content-Type": "application/json",
                "X-CSRF-Token": self.csrf_token
            }
        )
        
        # Check for security headers
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Content-Security-Policy'
        ]
        
        headers_present = 0
        for header in security_headers:
            if header in response.headers:
                headers_present += 1
                print(f"✅ Security header present: {header}")
            else:
                print(f"❌ Missing security header: {header}")
        
        if headers_present >= 3:
            print("✅ Session security headers implemented")
            return True
        else:
            print("❌ Insufficient security headers")
            return False
    
    def test_email_validation(self):
        """Test enhanced email validation"""
        print("\n📧 Testing Email Validation...")
        
        if not self.get_csrf_token():
            print("❌ Cannot test without CSRF token")
            return False
        
        # Test extremely long email (should be rejected)
        long_email = "a" * 300 + "@example.com"
        
        response = self.session.post(
            f"{self.base_url}/api/signup",
            json={"email": long_email, "password": "TestPass123!"},
            headers={
                "Content-Type": "application/json",
                "X-CSRF-Token": self.csrf_token
            }
        )
        
        if response.status_code == 400:
            print("✅ Long email validation working")
            return True
        else:
            print(f"❌ Long email validation failed - status: {response.status_code}")
            return False
    
    def run_all_tests(self):
        """Run all security tests"""
        print("🔐 FAAFO Career Platform - Security Fixes Validation")
        print("=" * 60)
        print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Testing: {self.base_url}")
        
        tests = [
            ("CSRF Protection", self.test_csrf_protection),
            ("Input Sanitization", self.test_input_sanitization),
            ("Rate Limiting", self.test_rate_limiting),
            ("Session Security", self.test_session_security),
            ("Email Validation", self.test_email_validation),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} test crashed: {e}")
                results.append((test_name, False))
        
        # Summary
        print(f"\n🎯 SECURITY VALIDATION SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"📊 Results: {passed}/{total} tests passed")
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}: {test_name}")
        
        if passed == total:
            print("\n🎉 ALL SECURITY TESTS PASSED!")
            print("✅ Security vulnerabilities have been successfully fixed")
        else:
            print(f"\n⚠️ {total - passed} security tests failed")
            print("❌ Additional security work required")
        
        return passed == total

def main():
    """Main execution"""
    # Check if server is running
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        print("✅ Development server detected")
    except:
        print("❌ Development server not running! Start with: npm run dev")
        sys.exit(1)
    
    # Run security tests
    tester = SecurityTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
